import './globals.css';
import type { Metadata } from 'next';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export const metadata: Metadata = {
  title: 'Kirkwood Medical Practice - Primary Care - Broome County, New York',
  description: 'Comprehensive primary care services in Kirkwood, NY. Serving Broome County families with personalized healthcare, same-day appointments, and experienced medical professionals.',
  keywords: 'primary care, family medicine, Kirkwood NY, Broome County, medical practice, same day appointments, telemedicine',
  icons: {
    icon: '/images/favicon.png',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className="min-h-screen flex flex-col">
        <a href="#main" className="skip-link">
          Skip to main content
        </a>
        <Header />
        <main id="main" className="flex-grow">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  );
}