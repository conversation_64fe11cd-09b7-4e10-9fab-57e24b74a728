import { MapPin, Clock, Phone, Calendar, Users, Heart } from 'lucide-react';
import { ScheduleAppointmentCTA, LearnMoreCTA, CallNowCTA } from '@/components/CTA';

export const metadata = {
  title: 'Service Areas | Kirkwood Medical Practice | Broome County Primary Care',
  description: 'Kirkwood Medical Practice serves patients throughout Broome County including Kirkwood, Binghamton, and Conklin with comprehensive primary care services.',
};

export default function Locations() {
  const locations = [
    {
      name: 'Kirkwood, NY',
      title: 'Our Home Base - Primary Care for Local Families',
      description: 'As Kirkwood\'s established primary care practice, we provide complete healthcare services designed specifically for our local community. Our providers understand the unique needs of Kirkwood families and deliver personalized care that builds lasting health partnerships.',
      highlights: [
        'Priority same-day appointment scheduling',
        'Complete family medicine for all ages',
        'Comprehensive insurance acceptance',
        'Local community commitment',
        'Multi-generational family care expertise'
      ],
      servicesOffered: [
        'Same-day sick visits and urgent care',
        'Annual physical examinations',
        'Chronic disease management',
        'Women\'s health services',
        'Telemedicine consultations',
        'Prescription management'
      ],
      link: '/locations/kirkwood',
      color: 'medical-blue'
    },
    {
      name: 'Binghamton, NY',
      title: 'Comprehensive Care for the Region\'s Largest City',
      description: 'Serving Broome County\'s largest city, we bring personalized primary care to Binghamton residents who deserve an alternative to large health system care. Our practice combines modern medical resources with personal attention that only an independent practice can provide.',
      highlights: [
        'Extended scheduling options for busy families',
        'Coordination with area specialists',
        'Professional care for working adults',
        'University area service availability',
        'Comprehensive chronic care management'
      ],
      servicesOffered: [
        'Family medicine for all ages',
        'Urgent care services',
        'Women\'s health & obstetrics coordination',
        'School & sports physicals',
        'Virtual care options',
        'Care coordination services'
      ],
      link: '/locations/binghamton',
      color: 'healing-teal'
    },
    {
      name: 'Conklin, NY',
      title: 'Accessible Healthcare for Rural Communities',
      description: 'Understanding that Conklin residents face unique challenges accessing quality healthcare, we provide comprehensive primary care services designed to meet rural community needs. Our practice bridges the gap between big city medical resources and small-town personal care.',
      highlights: [
        'Telehealth services to minimize travel',
        'Flexible scheduling for rural lifestyles',
        'Understanding of rural health challenges',
        'Coordination with regional healthcare services',
        'Cost-effective care options'
      ],
      servicesOffered: [
        'Comprehensive family care',
        'Same-day appointments',
        'Chronic disease monitoring',
        'Preventive health services',
        'Virtual healthcare visits',
        'Referral coordination'
      ],
      link: '/locations/conklin',
      color: 'care-red'
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-medical-blue to-trust-navy text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="font-source-sans font-bold text-brand-h1 lg:text-5xl mb-6">
              Serving Areas Throughout <span className="text-healing-teal">Broome County</span>
            </h1>
            <p className="font-open-sans text-xl mb-8 max-w-4xl mx-auto leading-relaxed text-blue-100">
              Convenient primary care throughout our service region, with providers who understand 
              local health challenges and provide care tailored to our community's unique needs.
            </p>
          </div>
        </div>
      </section>

      {/* Primary Location - Our Office */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="font-source-sans font-bold text-brand-h2 text-medical-blue mb-4">
              Our Central Location
            </h2>
            <p className="font-open-sans text-lg text-trust-navy max-w-3xl mx-auto">
              Conveniently located to serve families throughout Broome County with easy access 
              from major roadways and ample parking.
            </p>
          </div>
          
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <div className="grid grid-cols-1 lg:grid-cols-2">
              <div className="p-8">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-medical-blue rounded-lg flex items-center justify-center mr-4 text-white">
                    <MapPin className="h-6 w-6" />
                  </div>
                  <div>
                    <h3 className="font-source-sans font-bold text-2xl text-medical-blue">
                      Kirkwood Medical Practice
                    </h3>
                    <p className="font-open-sans text-healing-teal">Your Local Healthcare Partner</p>
                  </div>
                </div>
                
                <div className="space-y-4 mb-8">
                  <div className="flex items-start">
                    <MapPin className="h-5 w-5 text-care-red mr-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="font-source-sans font-semibold text-trust-navy">Address</p>
                      <p className="font-open-sans text-comfort-gray">
                        865 US-11<br />
                        Kirkwood, NY 13795
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <Phone className="h-5 w-5 text-care-red mr-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="font-source-sans font-semibold text-trust-navy">Phone</p>
                      <a href="tel:(607)608-3292" className="font-open-sans text-medical-blue hover:underline">
                        (*************
                      </a>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <Clock className="h-5 w-5 text-care-red mr-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="font-source-sans font-semibold text-trust-navy">Hours</p>
                      <p className="font-open-sans text-comfort-gray">
                        Monday - Friday: 8:00 AM - 5:00 PM<br />
                        Same-day appointments available
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <Calendar className="h-5 w-5 text-care-red mr-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="font-source-sans font-semibold text-trust-navy">Online Scheduling</p>
                      <a 
                        href="https://consumer.scheduling.athena.io/?locationId=31128-1"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="font-open-sans text-medical-blue hover:underline"
                      >
                        Schedule Your Appointment
                      </a>
                    </div>
                  </div>
                </div>
                
                <div className="flex flex-col sm:flex-row gap-4">
                  <CallNowCTA />
                  <ScheduleAppointmentCTA />
                </div>
              </div>
              
              <div className="bg-gray-100 h-80 lg:h-full flex items-center justify-center">
                <div className="text-center">
                  <div className="w-20 h-20 bg-medical-blue rounded-full flex items-center justify-center mx-auto mb-4">
                    <MapPin className="h-10 w-10 text-white" />
                  </div>
                  <p className="font-source-sans font-semibold text-trust-navy">
                    Interactive Map Coming Soon
                  </p>
                  <p className="font-open-sans text-comfort-gray text-sm">
                    Easy access from Route 11 with ample parking
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Service Areas */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="font-source-sans font-bold text-brand-h2 text-medical-blue mb-4">
              Communities We Proudly Serve
            </h2>
            <p className="font-open-sans text-lg text-trust-navy max-w-3xl mx-auto">
              Each community we serve has unique healthcare needs. Our providers understand 
              local health challenges and tailor our services accordingly.
            </p>
          </div>
          
          <div className="space-y-16">
            {locations.map((location, index) => (
              <div key={index} className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-start ${index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''}`}>
                <div className={`${index % 2 === 1 ? 'lg:col-start-2' : ''}`}>
                  <div className="bg-white rounded-lg shadow-lg p-8">
                    <div className="flex items-center mb-6">
                      <div className={`w-16 h-16 bg-${location.color} rounded-lg flex items-center justify-center mr-4 text-white`}>
                        <MapPin className="h-8 w-8" />
                      </div>
                      <div>
                        <h3 className="font-source-sans font-bold text-2xl text-medical-blue">
                          {location.name}
                        </h3>
                        <p className="font-open-sans text-healing-teal font-semibold">
                          {location.title}
                        </p>
                      </div>
                    </div>
                    
                    <p className="font-open-sans text-trust-navy mb-6 leading-relaxed">
                      {location.description}
                    </p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                      <div>
                        <h4 className="font-source-sans font-semibold text-lg text-medical-blue mb-3 flex items-center">
                          <Heart className="h-5 w-5 mr-2" />
                          Why Choose Us
                        </h4>
                        <ul className="space-y-2">
                          {location.highlights.map((highlight, highlightIndex) => (
                            <li key={highlightIndex} className="font-open-sans text-trust-navy text-sm flex items-start">
                              <div className="w-2 h-2 bg-healing-teal rounded-full mt-2 mr-3 flex-shrink-0"></div>
                              {highlight}
                            </li>
                          ))}
                        </ul>
                      </div>
                      
                      <div>
                        <h4 className="font-source-sans font-semibold text-lg text-medical-blue mb-3 flex items-center">
                          <Users className="h-5 w-5 mr-2" />
                          Services Available
                        </h4>
                        <ul className="space-y-2">
                          {location.servicesOffered.map((service, serviceIndex) => (
                            <li key={serviceIndex} className="font-open-sans text-trust-navy text-sm flex items-start">
                              <div className="w-2 h-2 bg-care-red rounded-full mt-2 mr-3 flex-shrink-0"></div>
                              {service}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                    
                    <LearnMoreCTA href={location.link}>
                      Learn More About {location.name} Services
                    </LearnMoreCTA>
                  </div>
                </div>
                
                <div className={`${index % 2 === 1 ? 'lg:col-start-1 lg:row-start-1' : ''}`}>
                  <div className={`bg-${location.color} rounded-lg h-80 flex items-center justify-center text-white`}>
                    <div className="text-center">
                      <div className="w-24 h-24 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <MapPin className="h-12 w-12" />
                      </div>
                      <h4 className="font-source-sans font-bold text-2xl mb-2">
                        {location.name}
                      </h4>
                      <p className="font-open-sans text-sm opacity-90">
                        Comprehensive Primary Care
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Regional Coverage */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="font-source-sans font-bold text-brand-h2 text-medical-blue mb-4">
              Extended Service Coverage
            </h2>
            <p className="font-open-sans text-lg text-trust-navy max-w-3xl mx-auto">
              While our main office is in Kirkwood, we proudly serve patients throughout Broome County 
              and surrounding areas with flexible scheduling and telehealth options.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white rounded-lg shadow-lg p-6 text-center">
              <div className="w-12 h-12 bg-medical-blue rounded-lg flex items-center justify-center mx-auto mb-4 text-white">
                <Clock className="h-6 w-6" />
              </div>
              <h3 className="font-source-sans font-semibold text-xl text-trust-navy mb-3">
                Flexible Scheduling
              </h3>
              <p className="font-open-sans text-comfort-gray">
                We accommodate patients from all service areas with flexible appointment times, 
                including early morning and extended hours when needed.
              </p>
            </div>
            
            <div className="bg-white rounded-lg shadow-lg p-6 text-center">
              <div className="w-12 h-12 bg-healing-teal rounded-lg flex items-center justify-center mx-auto mb-4 text-white">
                <Phone className="h-6 w-6" />
              </div>
              <h3 className="font-source-sans font-semibold text-xl text-trust-navy mb-3">
                Telehealth Options
              </h3>
              <p className="font-open-sans text-comfort-gray">
                Virtual consultations available for follow-up care, prescription management, 
                and routine healthcare needs, minimizing travel time.
              </p>
            </div>
            
            <div className="bg-white rounded-lg shadow-lg p-6 text-center">
              <div className="w-12 h-12 bg-care-red rounded-lg flex items-center justify-center mx-auto mb-4 text-white">
                <Heart className="h-6 w-6" />
              </div>
              <h3 className="font-source-sans font-semibold text-xl text-trust-navy mb-3">
                Local Understanding
              </h3>
              <p className="font-open-sans text-comfort-gray">
                Our providers understand the unique health challenges and lifestyle factors 
                that affect different communities throughout our service region.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-medical-blue text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="font-source-sans font-bold text-brand-h2 mb-6">
            Ready to Experience Local, Personal Healthcare?
          </h2>
          <p className="font-open-sans text-lg mb-8 leading-relaxed text-blue-100">
            No matter which community you call home in Broome County, Kirkwood Medical Practice 
            is here to serve you with expert, compassionate healthcare. Schedule your appointment 
            today and discover the difference that local, independent practice makes.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <ScheduleAppointmentCTA />
            <CallNowCTA />
          </div>
        </div>
      </section>
    </div>
  );
}