import Image from 'next/image';
import Link from 'next/link';
import { ArrowLeft, CheckCircle, MapPin, Clock, Monitor, Heart, Activity, UserCheck, User } from 'lucide-react';
import { getServiceBySlug, type ServiceData } from '@/lib/services-data';
import { ScheduleAppointmentCTA, LearnMoreCTA } from '@/components/CTA';

// Icon mapping
const iconMap = {
  Clock,
  Monitor,
  Heart,
  Activity,
  UserCheck,
  User,
};

interface ServicePageTemplateProps {
  slug: string;
}

export default function ServicePageTemplate({ slug }: ServicePageTemplateProps) {
  const service = getServiceBySlug(slug);

  if (!service) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-medical-blue mb-4">Service Not Found</h1>
          <Link href="/services" className="text-medical-blue hover:underline">
            Return to Services
          </Link>
        </div>
      </div>
    );
  }

  const IconComponent = iconMap[service.iconName as keyof typeof iconMap] || User;

  return (
    <div className="min-h-screen">
      {/* Breadcrumb Navigation */}
      <section className="bg-gray-50 py-4">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex items-center space-x-2 text-sm">
            <Link href="/" className="text-medical-blue hover:text-blue-800 transition-colors">
              Home
            </Link>
            <span className="text-comfort-gray">/</span>
            <Link href="/services" className="text-medical-blue hover:text-blue-800 transition-colors">
              Services
            </Link>
            <span className="text-comfort-gray">/</span>
            <span className="text-trust-navy font-medium">{service.title}</span>
          </nav>
        </div>
      </section>

      {/* Hero Section */}
      <section className={`relative bg-gradient-to-br from-${service.color} to-trust-navy text-white py-16`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="flex items-center mb-6">
                <div className="w-16 h-16 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-4">
                  <IconComponent className="h-8 w-8 text-white" />
                </div>
                <Link 
                  href="/services"
                  className="inline-flex items-center text-blue-100 hover:text-white transition-colors"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Services
                </Link>
              </div>
              
              <h1 className="font-source-sans font-bold text-brand-h1 lg:text-5xl mb-6">
                {service.h1}
              </h1>
              
              <p className="font-open-sans text-xl mb-8 leading-relaxed text-blue-100">
                {service.heroDescription}
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4">
                <ScheduleAppointmentCTA />
                <LearnMoreCTA href="/contact" className="bg-white bg-opacity-10 hover:bg-opacity-20 text-white border-white">
                  Contact Us About This Service
                </LearnMoreCTA>
              </div>
            </div>
            
            <div className="relative h-80 lg:h-96 rounded-lg overflow-hidden shadow-2xl">
              <Image
                src={service.image}
                alt={`${service.title} at Kirkwood Medical Practice`}
                width={600}
                height={400}
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-black bg-opacity-20"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Service Features */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="font-source-sans font-bold text-brand-h2 text-medical-blue mb-4">
              What We Provide
            </h2>
            <p className="font-open-sans text-lg text-trust-navy max-w-3xl mx-auto">
              Comprehensive {service.title.toLowerCase()} services designed to meet your specific healthcare needs.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {service.features.map((feature, index) => (
              <div key={index} className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-300">
                <div className="flex items-start">
                  <CheckCircle className="h-6 w-6 text-healing-teal mt-1 mr-3 flex-shrink-0" />
                  <p className="font-open-sans text-trust-navy leading-relaxed">
                    {feature}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Detailed Sections */}
      {service.sections.map((section, sectionIndex) => (
        <section key={sectionIndex} className={`py-16 ${sectionIndex % 2 === 1 ? 'bg-gray-50' : ''}`}>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="max-w-4xl mx-auto">
              <h2 className="font-source-sans font-bold text-brand-h2 text-medical-blue mb-6">
                {section.heading}
              </h2>
              
              <p className="font-open-sans text-lg text-trust-navy mb-8 leading-relaxed">
                {section.content}
              </p>
              
              {section.subsections && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {section.subsections.map((subsection, subIndex) => (
                    <div key={subIndex} className="bg-white rounded-lg shadow-md p-6">
                      <h3 className="font-source-sans font-semibold text-lg text-medical-blue mb-3">
                        {subsection.heading}
                      </h3>
                      <p className="font-open-sans text-trust-navy leading-relaxed">
                        {subsection.content}
                      </p>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </section>
      ))}

      {/* Service Areas */}
      <section className="py-16 bg-medical-blue text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="font-source-sans font-bold text-brand-h2 mb-4">
              Service Areas
            </h2>
            <p className="font-open-sans text-lg text-blue-100 max-w-3xl mx-auto">
              {service.title} available throughout our service region with convenient access and flexible scheduling.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {service.serviceAreas.map((area, index) => (
              <div key={index} className="bg-white bg-opacity-10 rounded-lg p-6">
                <div className="flex items-start">
                  <MapPin className="h-6 w-6 text-blue-200 mt-1 mr-3 flex-shrink-0" />
                  <p className="font-open-sans text-blue-100 leading-relaxed">
                    {area}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="font-source-sans font-bold text-brand-h2 text-medical-blue mb-4">
              Why Choose Our {service.title}
            </h2>
            <p className="font-open-sans text-lg text-trust-navy max-w-3xl mx-auto">
              Experience the difference that personalized, expert care makes in your healthcare journey.
            </p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {service.benefits.map((benefit, index) => (
              <div key={index} className="flex items-start">
                <div className={`w-12 h-12 bg-${service.color} rounded-lg flex items-center justify-center mr-4 flex-shrink-0`}>
                  <CheckCircle className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="font-open-sans text-trust-navy leading-relaxed">
                    {benefit}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Related Services */}
      {service.relatedServices && service.relatedServices.length > 0 && (
        <section className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="font-source-sans font-bold text-brand-h2 text-medical-blue mb-4">
                Related Services
              </h2>
              <p className="font-open-sans text-lg text-trust-navy">
                Explore our other healthcare services that complement your care.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {service.relatedServices.map((relatedSlug) => {
                const relatedService = getServiceBySlug(relatedSlug);
                if (!relatedService) return null;
                
                const RelatedIcon = iconMap[relatedService.iconName as keyof typeof iconMap] || User;
                
                return (
                  <Link
                    key={relatedSlug}
                    href={`/services/${relatedSlug}`}
                    className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-all duration-300 group"
                  >
                    <div className={`w-12 h-12 bg-${relatedService.color} rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                      <RelatedIcon className="h-6 w-6 text-white" />
                    </div>
                    <h3 className="font-source-sans font-semibold text-lg text-medical-blue mb-2 group-hover:text-blue-800 transition-colors">
                      {relatedService.title}
                    </h3>
                    <p className="font-open-sans text-comfort-gray text-sm leading-relaxed">
                      {relatedService.description}
                    </p>
                  </Link>
                );
              })}
            </div>
          </div>
        </section>
      )}

      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-medical-blue to-trust-navy text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="font-source-sans font-bold text-brand-h2 mb-6">
            Ready to Experience Quality {service.title}?
          </h2>
          <p className="font-open-sans text-xl mb-8 text-blue-100 leading-relaxed">
            Schedule your appointment today and discover the difference that personalized, expert healthcare makes.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <ScheduleAppointmentCTA />
            <LearnMoreCTA href="/contact" className="bg-white bg-opacity-10 hover:bg-opacity-20 text-white border-white">
              Contact Us
            </LearnMoreCTA>
          </div>
        </div>
      </section>
    </div>
  );
}
