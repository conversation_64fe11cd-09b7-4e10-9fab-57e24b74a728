import ServicePageTemplate from '@/components/ServicePageTemplate';
import { getServiceBySlug } from '@/lib/services-data';

export async function generateMetadata() {
  const service = getServiceBySlug('womens-health');
  
  if (!service) {
    return {
      title: 'Service Not Found | Kirkwood Medical Practice',
      description: 'The requested service page could not be found.',
    };
  }

  return {
    title: service.seoTitle,
    description: service.description,
    keywords: `${service.title}, Kirkwood Medical Practice, Broome County, primary care, womens-health`,
  };
}

export default function WomensHealthPage() {
  return <ServicePageTemplate slug="womens-health" />;
}
