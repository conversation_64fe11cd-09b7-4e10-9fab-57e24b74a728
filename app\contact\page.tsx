import ContactForm from '@/components/ContactForm';
import { Phone, MapPin, Clock, Calendar, Mail, MessageCircle } from 'lucide-react';
import { CallNowCTA } from '@/components/CTA';

export const metadata = {
  title: 'Contact Us | Kirkwood Medical Practice | Schedule Your Appointment',
  description: 'Contact Kirkwood Medical Practice to schedule your appointment. Call (************* or use our online form. Same-day appointments available.',
};

export default function Contact() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-medical-blue to-trust-navy text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="font-source-sans font-bold text-brand-h1 lg:text-5xl mb-6">
              Contact Kirkwood Medical Practice
            </h1>
            <p className="font-open-sans text-xl mb-8 max-w-4xl mx-auto leading-relaxed text-blue-100">
              Ready to experience personalized primary care? We're here to help you schedule your appointment 
              and answer any questions about our services. New patients are always welcome.
            </p>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div>
              <ContactForm />
            </div>
            
            {/* Contact Information */}
            <div className="space-y-8">
              {/* Main Contact Info */}
              <div className="bg-white rounded-lg shadow-lg p-8">
                <h2 className="font-source-sans font-bold text-2xl text-medical-blue mb-6">
                  Get In Touch
                </h2>
                
                <div className="space-y-6">
                  <div className="flex items-start">
                    <Phone className="h-6 w-6 text-care-red mr-4 mt-1 flex-shrink-0" />
                    <div>
                      <h3 className="font-source-sans font-semibold text-lg text-trust-navy mb-1">
                        Call Us Today
                      </h3>
                      <a 
                        href="tel:(607)608-3292" 
                        className="font-open-sans text-xl text-medical-blue hover:underline font-semibold"
                      >
                        (*************
                      </a>
                      <p className="font-open-sans text-sm text-comfort-gray mt-1">
                        Same-day appointments available for urgent needs
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <MapPin className="h-6 w-6 text-care-red mr-4 mt-1 flex-shrink-0" />
                    <div>
                      <h3 className="font-source-sans font-semibold text-lg text-trust-navy mb-1">
                        Visit Our Office
                      </h3>
                      <address className="font-open-sans text-comfort-gray not-italic">
                        865 US-11<br />
                        Kirkwood, NY 13795<br />
                        United States
                      </address>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <Clock className="h-6 w-6 text-care-red mr-4 mt-1 flex-shrink-0" />
                    <div>
                      <h3 className="font-source-sans font-semibold text-lg text-trust-navy mb-1">
                        Office Hours
                      </h3>
                      <div className="font-open-sans text-comfort-gray space-y-1">
                        <p>Monday - Friday: 8:00 AM - 5:00 PM</p>
                        <p>Saturday - Sunday: Closed</p>
                        <p className="text-sm text-medical-blue mt-2">
                          Extended hours available by appointment
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <Calendar className="h-6 w-6 text-care-red mr-4 mt-1 flex-shrink-0" />
                    <div>
                      <h3 className="font-source-sans font-semibold text-lg text-trust-navy mb-1">
                        Online Scheduling
                      </h3>
                      <a 
                        href="https://consumer.scheduling.athena.io/?locationId=31128-1" 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="font-open-sans text-medical-blue hover:underline"
                      >
                        Schedule Your Appointment Online
                      </a>
                      <p className="font-open-sans text-sm text-comfort-gray mt-1">
                        Available 24/7 for your convenience
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <MessageCircle className="h-6 w-6 text-care-red mr-4 mt-1 flex-shrink-0" />
                    <div>
                      <h3 className="font-source-sans font-semibold text-lg text-trust-navy mb-1">
                        Patient Portal
                      </h3>
                      <a 
                        href="https://31128.portal.athenahealth.com/" 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="font-open-sans text-medical-blue hover:underline"
                      >
                        Access Your Health Records
                      </a>
                      <p className="font-open-sans text-sm text-comfort-gray mt-1">
                        View test results, request prescription refills, and more
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Quick Actions */}
              <div className="bg-medical-blue rounded-lg p-8 text-white">
                <h3 className="font-source-sans font-bold text-xl mb-4">
                  Need Immediate Assistance?
                </h3>
                <p className="font-open-sans mb-6 text-blue-100">
                  For urgent medical needs or same-day appointments, call us directly. 
                  Our staff will work to accommodate your scheduling needs.
                </p>
                <CallNowCTA className="w-full" />
                
                <div className="mt-6 pt-6 border-t border-blue-300">
                  <h4 className="font-source-sans font-semibold mb-2">
                    After Hours Care
                  </h4>
                  <p className="font-open-sans text-sm text-blue-100">
                    For medical emergencies, call 911 or visit your nearest emergency room. 
                    For non-urgent after-hours questions, leave a message and we'll return 
                    your call the next business day.
                  </p>
                </div>
              </div>
              
              {/* Insurance Information */}
              <div className="bg-gray-50 rounded-lg p-8">
                <h3 className="font-source-sans font-bold text-xl text-medical-blue mb-4">
                  Insurance & Payment Information
                </h3>
                
                <div className="mb-6">
                  <h4 className="font-source-sans font-semibold text-trust-navy mb-3">
                    Insurance Plans Accepted
                  </h4>
                  <div className="grid grid-cols-2 gap-2 text-sm font-open-sans text-comfort-gray">
                    <div>• Aetna</div>
                    <div>• CI</div>
                    <div>• Empire BCBS</div>
                    <div>• Fidelis Care</div>
                    <div>• Humana</div>
                    <div>• Medicare</div>
                    <div>• Optum</div>
                    <div>• Most major plans</div>
                  </div>
                </div>
                
                <div className="bg-white p-4 rounded-lg">
                  <p className="font-open-sans text-sm text-trust-navy">
                    <strong>Note:</strong> Please call to verify your specific insurance coverage. 
                    We also accept cash payments and offer payment plan options for uninsured patients.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="font-source-sans font-bold text-brand-h2 text-medical-blue text-center mb-12">
            Frequently Asked Questions
          </h2>
          
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="font-source-sans font-semibold text-lg text-trust-navy mb-3">
                How quickly can I get an appointment?
              </h3>
              <p className="font-open-sans text-comfort-gray leading-relaxed">
                We offer same-day appointments for urgent medical needs and typically can schedule 
                routine appointments within 1-2 weeks. For new patients, we recommend calling as 
                early as possible to secure your preferred time slot.
              </p>
            </div>
            
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="font-source-sans font-semibold text-lg text-trust-navy mb-3">
                Do you accept new patients?
              </h3>
              <p className="font-open-sans text-comfort-gray leading-relaxed">
                Yes! We welcome new patients and are currently accepting new patient appointments. 
                Please call us or use our online form to schedule your initial consultation.
              </p>
            </div>
            
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="font-source-sans font-semibold text-lg text-trust-navy mb-3">
                What should I bring to my first appointment?
              </h3>
              <p className="font-open-sans text-comfort-gray leading-relaxed">
                Please bring a valid photo ID, your insurance card, current medications list, 
                any recent medical records from previous providers, and a list of questions 
                or concerns you'd like to discuss.
              </p>
            </div>
            
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="font-source-sans font-semibold text-lg text-trust-navy mb-3">
                Do you offer telemedicine appointments?
              </h3>
              <p className="font-open-sans text-comfort-gray leading-relaxed">
                Yes, we offer secure telemedicine consultations for follow-up care, prescription 
                management, chronic condition monitoring, and many routine healthcare needs. 
                Ask about virtual visit options when scheduling.
              </p>
            </div>
            
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="font-source-sans font-semibold text-lg text-trust-navy mb-3">
                What areas do you serve?
              </h3>
              <p className="font-open-sans text-comfort-gray leading-relaxed">
                We proudly serve patients throughout Broome County, including Kirkwood, Binghamton, 
                Conklin, and surrounding communities. Our central Kirkwood location provides convenient 
                access for families throughout the region.
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}