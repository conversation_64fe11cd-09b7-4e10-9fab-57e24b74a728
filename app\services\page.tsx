import Image from 'next/image';
import {
  User,
  Clock,
  Monitor,
  Heart,
  Activity,
  UserCheck,
  Pill,
  Apple,
  Phone,
  Calendar,
  ArrowRight
} from 'lucide-react';
import { ScheduleAppointmentCTA, LearnMoreCTA } from '@/components/CTA';

export const metadata = {
  title: 'Our Services | Kirkwood Medical Practice | Primary Care Broome County',
  description: 'Comprehensive primary care services including same-day appointments, family medicine, telemedicine, women\'s health, chronic care management, and annual physicals.',
};

export default function Services() {
  const services = [
    {
      icon: <User className="h-8 w-8" />,
      title: 'Family Medicine & Primary Care',
      description: 'Comprehensive healthcare for all ages with personalized attention from providers who understand your family\'s unique health needs and history.',
      features: [
        'Complete medical evaluations for all family members',
        'Preventive care and wellness planning',
        'Acute illness diagnosis and treatment',
        'Health maintenance and screening coordination',
        'Multi-generational family care expertise'
      ],
      link: '/services/family-medicine',
      color: 'medical-blue',
      image: '/images/services-images/doctor-patient-sitting-together-at-desk.jpeg'
    },
    {
      icon: <Clock className="h-8 w-8" />,
      title: 'Same-Day Sick Visits & Urgent Care',
      description: 'When illness strikes unexpectedly, we provide immediate medical attention without long waits or impersonal care of urgent care centers.',
      features: [
        'Same-day appointment availability',
        'Walk-in hours for urgent needs',
        'Minor injury and illness treatment',
        'Rapid assessment and diagnosis',
        'On-site treatment capabilities'
      ],
      link: '/services/same-day-care',
      color: 'care-red',
      image: '/images/services-images/octor performing a non-intrusive part of an exam.jpg'
    },
    {
      icon: <Monitor className="h-8 w-8" />,
      title: 'Telemedicine & Virtual Consultations',
      description: 'Modern healthcare delivery that brings medical expertise directly to your home with secure, convenient virtual consultations.',
      features: [
        'Follow-up consultations via secure video',
        'Chronic condition monitoring',
        'Prescription refill consultations',
        'Mental health check-ins',
        'Lab result reviews and explanations'
      ],
      link: '/services/telemedicine',
      color: 'healing-teal',
      image: '/images/services-images/patient-virtual-consultation.jpeg'
    },
    {
      icon: <Heart className="h-8 w-8" />,
      title: 'Women\'s Health Services',
      description: 'Specialized healthcare designed for women\'s unique health needs, providing compassionate care throughout all life stages.',
      features: [
        'Annual gynecological examinations',
        'Family planning and contraception counseling',
        'Prenatal care coordination',
        'Menopause support and hormone therapy',
        'Reproductive health screenings'
      ],
      link: '/services/womens-health',
      color: 'medical-blue',
      image: '/images/services-images/female-physician-private-consultation-with-female-patient.jpg'
    },
    {
      icon: <Activity className="h-8 w-8" />,
      title: 'Chronic Condition Management',
      description: 'Expert ongoing care for chronic conditions that improves quality of life and prevents complications through systematic monitoring.',
      features: [
        'Diabetes monitoring and treatment',
        'Hypertension management',
        'Heart disease care coordination',
        'Arthritis pain management',
        'Medication optimization and lifestyle counseling'
      ],
      link: '/services/chronic-care',
      color: 'healing-teal',
      image: '/images/services-images/Blood-glucose-monitoring.jpeg'
    },
    {
      icon: <UserCheck className="h-8 w-8" />,
      title: 'Annual & School Physicals',
      description: 'Thorough health assessments for wellness maintenance, school requirements, sports participation, and employment needs.',
      features: [
        'Comprehensive annual wellness examinations',
        'School enrollment physical exams',
        'Sports participation physicals',
        'Pre-employment health screenings',
        'Age-appropriate health screenings'
      ],
      link: '/services/physicals',
      color: 'care-red',
      image: '/images/services-images/doctor-examining-child-ear.png'
    }
  ];

  const additionalServices = [
    {
      icon: <Pill className="h-6 w-6" />,
      title: 'Prescription Management',
      description: 'Convenient prescription refills and medication reviews with electronic prescribing to local pharmacies.'
    },
    {
      icon: <Apple className="h-6 w-6" />,
      title: 'Nutritional Counseling',
      description: 'Personalized dietary guidance and lifestyle modification support to improve health outcomes.'
    },
    {
      icon: <Phone className="h-6 w-6" />,
      title: 'Care Coordination',
      description: 'Seamless referrals and communication with specialists and hospitals for comprehensive care.'
    },
    {
      icon: <Calendar className="h-6 w-6" />,
      title: 'Preventive Care Planning',
      description: 'Age-appropriate screenings, vaccinations, and wellness planning for optimal health maintenance.'
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-medical-blue to-trust-navy text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="font-source-sans font-bold text-brand-h1 lg:text-5xl mb-6">
              Complete Primary Care Solutions
            </h1>
            <p className="font-open-sans text-xl mb-8 max-w-4xl mx-auto leading-relaxed text-blue-100">
              From same-day sick visits to ongoing chronic care management, we provide comprehensive healthcare 
              services designed to meet all your family's medical needs with personalized attention and expert care.
            </p>
          </div>
        </div>
      </section>

      {/* Core Services */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="font-source-sans font-bold text-brand-h2 text-medical-blue mb-4">
              Our Core Services
            </h2>
            <p className="font-open-sans text-lg text-trust-navy max-w-3xl mx-auto">
              Comprehensive healthcare services delivered with the personal attention and clinical expertise 
              that only an independent practice can provide.
            </p>
          </div>
          
          <div className="space-y-16">
            {services.map((service, index) => (
              <div key={index} className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''}`}>
                <div className={`${index % 2 === 1 ? 'lg:col-start-2' : ''}`}>
                  <div className="bg-white rounded-lg shadow-lg p-8">
                    <div className="flex items-center mb-6">
                      <div className={`w-16 h-16 bg-${service.color} rounded-lg flex items-center justify-center mr-4 text-white`}>
                        {service.icon}
                      </div>
                      <h3 className="font-source-sans font-bold text-2xl text-medical-blue">
                        {service.title}
                      </h3>
                    </div>
                    
                    <p className="font-open-sans text-trust-navy mb-6 leading-relaxed">
                      {service.description}
                    </p>
                    
                    <div className="mb-6">
                      <h4 className="font-source-sans font-semibold text-lg text-medical-blue mb-3">
                        What We Provide:
                      </h4>
                      <ul className="space-y-2">
                        {service.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="font-open-sans text-trust-navy flex items-start">
                            <div className="w-2 h-2 bg-healing-teal rounded-full mt-2 mr-3 flex-shrink-0"></div>
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <LearnMoreCTA href={service.link}>
                      Learn More About {service.title.split(' ')[0]} {service.title.split(' ')[1]}
                    </LearnMoreCTA>
                  </div>
                </div>
                
                <div className={`${index % 2 === 1 ? 'lg:col-start-1 lg:row-start-1' : ''}`}>
                  <div className="relative h-80 rounded-lg overflow-hidden shadow-lg">
                    <Image
                      src={service.image}
                      alt={`${service.title} at Kirkwood Medical Practice`}
                      width={600}
                      height={320}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-20"></div>
                    <div className="absolute bottom-4 left-4">
                      <div className={`w-12 h-12 bg-${service.color} rounded-lg flex items-center justify-center text-white shadow-lg`}>
                        {service.icon}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Additional Services */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="font-source-sans font-bold text-brand-h2 text-medical-blue mb-4">
              Additional Services & Support
            </h2>
            <p className="font-open-sans text-lg text-trust-navy max-w-3xl mx-auto">
              Beyond our core services, we provide comprehensive support services to ensure 
              all aspects of your healthcare needs are addressed.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {additionalServices.map((service, index) => (
              <div key={index} className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-300">
                <div className="flex items-start">
                  <div className="w-12 h-12 bg-medical-blue rounded-lg flex items-center justify-center mr-4 text-white flex-shrink-0">
                    {service.icon}
                  </div>
                  <div>
                    <h3 className="font-source-sans font-semibold text-lg text-trust-navy mb-2">
                      {service.title}
                    </h3>
                    <p className="font-open-sans text-comfort-gray leading-relaxed">
                      {service.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Service Areas */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="font-source-sans font-bold text-brand-h2 text-medical-blue mb-4">
              Convenient Service Throughout Broome County
            </h2>
            <p className="font-open-sans text-lg text-trust-navy max-w-3xl mx-auto">
              All our services are available to patients throughout our service areas, 
              with flexible scheduling and telehealth options for your convenience.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                area: 'Kirkwood, NY',
                description: 'Our home base with full service availability and same-day appointment priority'
              },
              {
                area: 'Binghamton, NY',
                description: 'Complete services for the region\'s largest city with extended scheduling options'
              },
              {
                area: 'Conklin, NY',
                description: 'Accessible healthcare for rural communities with telehealth support'
              }
            ].map((location, index) => (
              <div key={index} className="bg-medical-blue rounded-lg p-6 text-white text-center">
                <h3 className="font-source-sans font-bold text-xl mb-3">
                  {location.area}
                </h3>
                <p className="font-open-sans text-blue-100">
                  {location.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Insurance & Accessibility */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="font-source-sans font-bold text-brand-h2 text-medical-blue mb-6">
            Accessible Care for All Patients
          </h2>
          <p className="font-open-sans text-lg text-trust-navy mb-8 leading-relaxed">
            We accept most major insurance plans and work with you to ensure our services are accessible and affordable.
          </p>
          
          <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h3 className="font-source-sans font-semibold text-xl text-medical-blue mb-4">
              Insurance Plans Accepted
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center font-open-sans text-trust-navy">
              <div>Aetna</div>
              <div>CI</div>
              <div>Empire BCBS</div>
              <div>Fidelis Care</div>
              <div>Humana</div>
              <div>Medicare</div>
              <div>Optum</div>
              <div>Most Major Plans</div>
            </div>
            <p className="font-open-sans text-sm text-comfort-gray mt-4">
              Don't see your insurance listed? Call us to verify coverage for your specific plan.
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <ScheduleAppointmentCTA />
            <LearnMoreCTA href="/contact">
              Contact Us About Services
            </LearnMoreCTA>
          </div>
        </div>
      </section>
    </div>
  );
}