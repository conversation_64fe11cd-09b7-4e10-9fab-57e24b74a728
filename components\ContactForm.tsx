'use client';

import { useState } from 'react';
import { Mail, Phone, MessageSquare, User, Calendar, Clock } from 'lucide-react';

interface FormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  preferredContact: 'email' | 'phone';
  appointmentType: string;
  message: string;
}

interface FormErrors {
  [key: string]: string;
}

export default function ContactForm() {
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    preferredContact: 'phone',
    appointmentType: '',
    message: '',
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const appointmentTypes = [
    'Annual Physical',
    'Same-Day Sick Visit',
    'Chronic Care Follow-up',
    'Women\'s Health',
    'New Patient Consultation',
    'Other',
  ];

  const validateEmail = (email: string) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  const validatePhone = (phone: string) => {
    const cleaned = phone.replace(/\D/g, '');
    return cleaned.length === 10;
  };

  const validateForm = () => {
    const newErrors: FormErrors = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!validateEmail(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    } else if (!validatePhone(formData.phone)) {
      newErrors.phone = 'Please enter a valid 10-digit phone number';
    }

    if (!formData.appointmentType) {
      newErrors.appointmentType = 'Please select an appointment type';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const formatPhoneNumber = (value: string) => {
    const numbers = value.replace(/\D/g, '');
    if (numbers.length <= 3) return numbers;
    if (numbers.length <= 6) return `(${numbers.slice(0, 3)}) ${numbers.slice(3)}`;
    return `(${numbers.slice(0, 3)}) ${numbers.slice(3, 6)}-${numbers.slice(6, 10)}`;
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPhoneNumber(e.target.value);
    setFormData(prev => ({ ...prev, phone: formatted }));
    
    if (errors.phone) {
      setErrors(prev => ({ ...prev, phone: '' }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    setIsSubmitting(false);
    setIsSubmitted(true);
  };

  if (isSubmitted) {
    return (
      <div className="bg-clean-white rounded-lg shadow-lg p-8 text-center">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <h3 className="text-2xl font-source-sans font-bold text-medical-blue mb-4">
          Thank You!
        </h3>
        <p className="font-open-sans text-trust-navy mb-6">
          Your message has been received. Our team will contact you within 1 business day to schedule your appointment.
        </p>
        <div className="space-y-2 text-sm font-open-sans text-comfort-gray">
          <p>For urgent medical needs, please call: <strong>(607) 608-3292</strong></p>
          <p>Or schedule online: <a href="https://consumer.scheduling.athena.io/?locationId=31128-1" className="text-medical-blue hover:underline">Patient Scheduling Portal</a></p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-clean-white rounded-lg shadow-lg p-8">
      <div className="mb-6">
        <h3 className="text-2xl font-source-sans font-bold text-medical-blue mb-2">
          Request an Appointment
        </h3>
        <p className="font-open-sans text-trust-navy">
          Complete the form below and we'll contact you to schedule your visit.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Name Fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="firstName" className="block font-open-sans font-medium text-trust-navy mb-2">
              First Name *
            </label>
            <div className="relative">
              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-comfort-gray" />
              <input
                type="text"
                id="firstName"
                name="firstName"
                value={formData.firstName}
                onChange={handleInputChange}
                className={`w-full pl-10 pr-4 py-3 border rounded-lg font-open-sans focus:outline-none focus:ring-2 focus:ring-medical-blue focus:border-transparent transition-colors ${
                  errors.firstName ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter your first name"
                aria-describedby={errors.firstName ? 'firstName-error' : undefined}
              />
            </div>
            {errors.firstName && (
              <p id="firstName-error" className="mt-1 text-sm text-red-600 font-open-sans">
                {errors.firstName}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="lastName" className="block font-open-sans font-medium text-trust-navy mb-2">
              Last Name *
            </label>
            <div className="relative">
              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-comfort-gray" />
              <input
                type="text"
                id="lastName"
                name="lastName"
                value={formData.lastName}
                onChange={handleInputChange}
                className={`w-full pl-10 pr-4 py-3 border rounded-lg font-open-sans focus:outline-none focus:ring-2 focus:ring-medical-blue focus:border-transparent transition-colors ${
                  errors.lastName ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter your last name"
                aria-describedby={errors.lastName ? 'lastName-error' : undefined}
              />
            </div>
            {errors.lastName && (
              <p id="lastName-error" className="mt-1 text-sm text-red-600 font-open-sans">
                {errors.lastName}
              </p>
            )}
          </div>
        </div>

        {/* Contact Fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="email" className="block font-open-sans font-medium text-trust-navy mb-2">
              Email Address *
            </label>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-comfort-gray" />
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className={`w-full pl-10 pr-4 py-3 border rounded-lg font-open-sans focus:outline-none focus:ring-2 focus:ring-medical-blue focus:border-transparent transition-colors ${
                  errors.email ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="<EMAIL>"
                aria-describedby={errors.email ? 'email-error' : undefined}
              />
            </div>
            {errors.email && (
              <p id="email-error" className="mt-1 text-sm text-red-600 font-open-sans">
                {errors.email}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="phone" className="block font-open-sans font-medium text-trust-navy mb-2">
              Phone Number *
            </label>
            <div className="relative">
              <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-comfort-gray" />
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handlePhoneChange}
                className={`w-full pl-10 pr-4 py-3 border rounded-lg font-open-sans focus:outline-none focus:ring-2 focus:ring-medical-blue focus:border-transparent transition-colors ${
                  errors.phone ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="(*************"
                aria-describedby={errors.phone ? 'phone-error' : undefined}
              />
            </div>
            {errors.phone && (
              <p id="phone-error" className="mt-1 text-sm text-red-600 font-open-sans">
                {errors.phone}
              </p>
            )}
          </div>
        </div>

        {/* Appointment Type */}
        <div>
          <label htmlFor="appointmentType" className="block font-open-sans font-medium text-trust-navy mb-2">
            Appointment Type *
          </label>
          <div className="relative">
            <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-comfort-gray" />
            <select
              id="appointmentType"
              name="appointmentType"
              value={formData.appointmentType}
              onChange={handleInputChange}
              className={`w-full pl-10 pr-4 py-3 border rounded-lg font-open-sans focus:outline-none focus:ring-2 focus:ring-medical-blue focus:border-transparent transition-colors appearance-none bg-white ${
                errors.appointmentType ? 'border-red-500' : 'border-gray-300'
              }`}
              aria-describedby={errors.appointmentType ? 'appointmentType-error' : undefined}
            >
              <option value="">Select appointment type</option>
              {appointmentTypes.map(type => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
          </div>
          {errors.appointmentType && (
            <p id="appointmentType-error" className="mt-1 text-sm text-red-600 font-open-sans">
              {errors.appointmentType}
            </p>
          )}
        </div>

        {/* Preferred Contact Method */}
        <fieldset>
          <legend className="block font-open-sans font-medium text-trust-navy mb-2">
            Preferred Contact Method
          </legend>
          <div className="flex space-x-4">
            <label className="flex items-center cursor-pointer">
              <input
                type="radio"
                name="preferredContact"
                value="phone"
                checked={formData.preferredContact === 'phone'}
                onChange={handleInputChange}
                className="h-4 w-4 text-medical-blue border-gray-300 focus:ring-medical-blue"
              />
              <span className="ml-2 font-open-sans text-trust-navy">Phone</span>
            </label>
            <label className="flex items-center cursor-pointer">
              <input
                type="radio"
                name="preferredContact"
                value="email"
                checked={formData.preferredContact === 'email'}
                onChange={handleInputChange}
                className="h-4 w-4 text-medical-blue border-gray-300 focus:ring-medical-blue"
              />
              <span className="ml-2 font-open-sans text-trust-navy">Email</span>
            </label>
          </div>
        </fieldset>

        {/* Message */}
        <div>
          <label htmlFor="message" className="block font-open-sans font-medium text-trust-navy mb-2">
            Additional Information
          </label>
          <div className="relative">
            <MessageSquare className="absolute left-3 top-3 h-5 w-5 text-comfort-gray" />
            <textarea
              id="message"
              name="message"
              value={formData.message}
              onChange={handleInputChange}
              rows={4}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg font-open-sans focus:outline-none focus:ring-2 focus:ring-medical-blue focus:border-transparent transition-colors resize-vertical"
              placeholder="Please share any specific concerns, symptoms, or questions you'd like to discuss during your appointment."
            />
          </div>
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          disabled={isSubmitting}
          className="w-full bg-care-red hover:bg-red-700 disabled:bg-gray-400 text-white font-source-sans font-semibold py-4 px-6 rounded-lg transition-colors duration-200 min-h-[44px] flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-care-red focus:ring-offset-2"
        >
          {isSubmitting ? (
            <>
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Submitting...
            </>
          ) : (
            'Request Appointment'
          )}
        </button>

        {/* Quick Action Links */}
        <div className="text-center pt-4 border-t border-gray-200">
          <p className="font-open-sans text-sm text-comfort-gray mb-3">
            Need immediate assistance?
          </p>
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <a
              href="tel:(607)608-3292"
              className="inline-flex items-center justify-center px-4 py-2 border border-medical-blue text-medical-blue hover:bg-medical-blue hover:text-white font-source-sans font-semibold rounded-lg transition-colors min-h-[44px] focus:outline-none focus:ring-2 focus:ring-medical-blue focus:ring-offset-2"
            >
              <Phone className="h-4 w-4 mr-2" />
              Call Now
            </a>
            <a
              href="https://consumer.scheduling.athena.io/?locationId=31128-1"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center justify-center px-4 py-2 border border-healing-teal text-healing-teal hover:bg-healing-teal hover:text-white font-source-sans font-semibold rounded-lg transition-colors min-h-[44px] focus:outline-none focus:ring-2 focus:ring-healing-teal focus:ring-offset-2"
            >
              <Clock className="h-4 w-4 mr-2" />
              Schedule Online
            </a>
          </div>
        </div>
      </form>
    </div>
  );
}