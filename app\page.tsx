import Image from 'next/image';
import { 
  ScheduleAppointmentCTA, 
  CallNowCTA, 
  NewPatientCTA,
  LearnMoreCTA 
} from '@/components/CTA';
import { 
  InsuranceTrustBadge, 
  ExperienceTrustBadge, 
  AvailabilityTrustBadge,
  QualityTrustBadge,
  CommunityTrustBadge,
  CertificationTrustBadge 
} from '@/components/TrustBadge';
import { 
  Heart, 
  Clock, 
  Users, 
  Phone, 
  MapPin, 
  CheckCircle, 
  Calendar,
  Monitor,
  User,
  Activity,
  Pill,
  UserCheck,
  Apple,
  ArrowRight,
  Star
} from 'lucide-react';
import ContactForm from '@/components/ContactForm';

export default function Home() {
  const services = [
    {
      icon: <User className="h-6 w-6" />,
      title: 'Family Medicine & Primary Care',
      description: 'Comprehensive healthcare for all ages with personalized attention from providers who know your family.',
      link: '/services/family-medicine'
    },
    {
      icon: <Clock className="h-6 w-6" />,
      title: 'Same-Day Sick Visits',
      description: 'When illness strikes, we\'re here today. No long waits - just prompt, expert medical care.',
      link: '/services/same-day-care'
    },
    {
      icon: <Monitor className="h-6 w-6" />,
      title: 'Telemedicine & Virtual Care',
      description: 'Healthcare from the comfort of home with secure video consultations and remote monitoring.',
      link: '/services/telemedicine'
    },
    {
      icon: <Heart className="h-6 w-6" />,
      title: 'Women\'s Health Services',
      description: 'Specialized care for women\'s unique health needs throughout all stages of life.',
      link: '/services/womens-health'
    },
    {
      icon: <Activity className="h-6 w-6" />,
      title: 'Chronic Condition Management',
      description: 'Expert ongoing care for diabetes, hypertension, and other chronic conditions.',
      link: '/services/chronic-care'
    },
    {
      icon: <UserCheck className="h-6 w-6" />,
      title: 'Annual & School Physicals',
      description: 'Comprehensive health assessments for wellness, school, sports, and employment requirements.',
      link: '/services/physicals'
    },
  ];

  const locations = [
    {
      name: 'Kirkwood, NY',
      description: 'Our home base serving the local Kirkwood community',
      link: '/locations/kirkwood'
    },
    {
      name: 'Binghamton, NY',
      description: 'Comprehensive care for the region\'s largest city',
      link: '/locations/binghamton'
    },
    {
      name: 'Conklin, NY',
      description: 'Accessible healthcare for rural communities',
      link: '/locations/conklin'
    },
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-medical-blue to-trust-navy text-white py-20 overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-10"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="font-source-sans font-bold text-brand-h1 lg:text-5xl mb-6 leading-tight">
                Comprehensive Primary Care Services in <span className="text-healing-teal">Kirkwood, NY</span>
              </h1>
              <p className="font-open-sans text-xl mb-8 leading-relaxed text-blue-100">
                Personalized primary care for Broome County families who deserve an alternative to large health system care. 
                We build lasting health partnerships through experienced, compassionate providers who know your family.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 mb-8">
                <ScheduleAppointmentCTA />
                <CallNowCTA />
              </div>
              <div className="flex items-center space-x-6 text-sm">
                <div className="flex items-center">
                  <CheckCircle className="h-5 w-5 mr-2 text-healing-teal" />
                  <span>Same-Day Appointments</span>
                </div>
                <div className="flex items-center">
                  <CheckCircle className="h-5 w-5 mr-2 text-healing-teal" />
                  <span>New Patients Welcome</span>
                </div>
              </div>
            </div>
            
            <div className="hidden lg:block">
              <div className="relative">
                <Image
                  src="/images/doctor-with-patient.jpg"
                  alt="Kirkwood Medical Practice - Compassionate healthcare providers serving Broome County families"
                  width={600}
                  height={400}
                  className="rounded-lg shadow-2xl"
                  priority
                />
                <div className="absolute -bottom-6 -left-6 bg-white p-6 rounded-lg shadow-lg">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-medical-blue rounded-full flex items-center justify-center">
                      <Heart className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <p className="font-source-sans font-bold text-medical-blue">Multi-Generational Care</p>
                      <p className="font-open-sans text-sm text-comfort-gray">Serving families for decades</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Trust Badges */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="font-source-sans font-bold text-brand-h2 text-medical-blue mb-4">
              Why Choose Kirkwood Medical Practice
            </h2>
            <p className="font-open-sans text-lg text-trust-navy max-w-3xl mx-auto">
              Personalized care that large health systems cannot provide, delivered by experienced providers 
              committed to your community's health and wellbeing.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <AvailabilityTrustBadge />
            <InsuranceTrustBadge />
            <ExperienceTrustBadge />
            <QualityTrustBadge />
            <CommunityTrustBadge />
            <CertificationTrustBadge />
          </div>
        </div>
      </section>

      {/* Our Story */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="font-source-sans font-bold text-brand-h2 text-medical-blue mb-6">
                Our Story
              </h2>
              <div className="space-y-4 font-open-sans text-brand-body text-trust-navy leading-relaxed">
                <p>
                  Kirkwood Medical Practice is bringing personalized primary care to Broome County families 
                  who deserve an alternative to large health system care. Our practice is founded on the 
                  principle that quality healthcare requires personal relationships, dedicated time with 
                  patients, and a deep commitment to community health.
                </p>
                <p>
                  We're establishing our practice to serve multiple generations of families, from growing 
                  families to aging seniors, with the continuity of care that builds lasting health partnerships. 
                  Our experienced providers understand that every patient is unique, and we take the time to 
                  listen and develop personalized care plans.
                </p>
                <p>
                  At Kirkwood Medical Practice, you're not just a number in a large system - you're a valued 
                  member of our community, and your health is our priority.
                </p>
              </div>
              <div className="mt-8">
                <LearnMoreCTA href="/about">
                  Meet Our Medical Team
                </LearnMoreCTA>
              </div>
            </div>
            
            <div>
              <Image
                src="/images/kirkwood-medical-practice-clinic-exterior-view.jpg"
                alt="Kirkwood Medical Practice clinic exterior view"
                width={600}
                height={400}
                className="rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="font-source-sans font-bold text-brand-h2 text-medical-blue mb-4">
              Complete Primary Care Solutions
            </h2>
            <p className="font-open-sans text-lg text-trust-navy max-w-3xl mx-auto">
              From same-day sick visits to ongoing chronic care management, we provide comprehensive 
              healthcare services designed to meet all your family's medical needs.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <div key={index} className="bg-white rounded-lg shadow-lg p-8 hover:shadow-xl transition-shadow duration-300">
                <div className="w-12 h-12 bg-medical-blue rounded-lg flex items-center justify-center mb-4 text-white">
                  {service.icon}
                </div>
                <h3 className="font-source-sans font-semibold text-brand-h3 text-trust-navy mb-3">
                  {service.title}
                </h3>
                <p className="font-open-sans text-comfort-gray mb-4 leading-relaxed">
                  {service.description}
                </p>
                <LearnMoreCTA href={service.link}>
                  Learn More
                </LearnMoreCTA>
              </div>
            ))}
          </div>
          
          <div className="text-center mt-12">
            <LearnMoreCTA href="/services">
              View All Services
            </LearnMoreCTA>
          </div>
        </div>
      </section>

      {/* Service Areas */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="font-source-sans font-bold text-brand-h2 text-medical-blue mb-4">
              Serving Areas Throughout Broome County
            </h2>
            <p className="font-open-sans text-lg text-trust-navy max-w-3xl mx-auto">
              Convenient primary care throughout our service region, with providers who understand 
              local health challenges and provide care tailored to our community's needs.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {locations.map((location, index) => (
              <div key={index} className="bg-white rounded-lg shadow-lg p-8 text-center hover:shadow-xl transition-shadow duration-300">
                <div className="w-12 h-12 bg-healing-teal rounded-lg flex items-center justify-center mx-auto mb-4 text-white">
                  <MapPin className="h-6 w-6" />
                </div>
                <h3 className="font-source-sans font-semibold text-brand-h3 text-trust-navy mb-3">
                  {location.name}
                </h3>
                <p className="font-open-sans text-comfort-gray mb-4">
                  {location.description}
                </p>
                <LearnMoreCTA href={location.link}>
                  Learn More
                </LearnMoreCTA>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Insurance Providers Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="font-source-sans font-bold text-brand-h2 text-medical-blue mb-4">
              Insurance Plans Accepted
            </h2>
            <p className="font-open-sans text-lg text-trust-navy max-w-3xl mx-auto">
              We accept most major insurance plans to ensure our services are accessible and affordable for all patients.
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center">
            <div className="flex justify-center">
              <Image
                src="/images/insurance-providers/aetna-insurance-logo.png"
                alt="Aetna Insurance"
                width={120}
                height={60}
                className="h-12 w-auto object-contain opacity-70 hover:opacity-100 transition-opacity duration-300"
              />
            </div>
            <div className="flex justify-center">
              <Image
                src="/images/insurance-providers/empire-bluecross-blueshield-logo.jpg"
                alt="Empire BlueCross BlueShield"
                width={120}
                height={60}
                className="h-12 w-auto object-contain opacity-70 hover:opacity-100 transition-opacity duration-300"
              />
            </div>
            <div className="flex justify-center">
              <Image
                src="/images/insurance-providers/fidelis-care-insurance-logo.png"
                alt="Fidelis Care"
                width={120}
                height={60}
                className="h-12 w-auto object-contain opacity-70 hover:opacity-100 transition-opacity duration-300"
              />
            </div>
            <div className="flex justify-center">
              <Image
                src="/images/insurance-providers/humana-insurance-logo.jpg"
                alt="Humana"
                width={120}
                height={60}
                className="h-12 w-auto object-contain opacity-70 hover:opacity-100 transition-opacity duration-300"
              />
            </div>
            <div className="flex justify-center">
              <Image
                src="/images/insurance-providers/Medicare-Emblem.png"
                alt="Medicare"
                width={120}
                height={60}
                className="h-12 w-auto object-contain opacity-70 hover:opacity-100 transition-opacity duration-300"
              />
            </div>
            <div className="flex justify-center">
              <Image
                src="/images/insurance-providers/optum-insurance-logo.png"
                alt="Optum"
                width={120}
                height={60}
                className="h-12 w-auto object-contain opacity-70 hover:opacity-100 transition-opacity duration-300"
              />
            </div>
          </div>

          <div className="text-center mt-8">
            <p className="font-open-sans text-comfort-gray">
              Don't see your insurance listed? <a href="/contact" className="text-medical-blue hover:underline">Contact us</a> to verify coverage for your specific plan.
            </p>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="font-source-sans font-bold text-brand-h2 text-medical-blue mb-4">
              Schedule Your Appointment Today
            </h2>
            <p className="font-open-sans text-lg text-trust-navy max-w-3xl mx-auto">
              Ready to experience personalized primary care? New patients are welcome, and we offer 
              flexible scheduling including same-day appointments for urgent needs.
            </p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <ContactForm />
            
            <div className="space-y-8">
              <div className="bg-white rounded-lg shadow-lg p-8">
                <h3 className="font-source-sans font-bold text-xl text-medical-blue mb-6">
                  Contact Information
                </h3>
                
                <div className="space-y-4">
                  <div className="flex items-start">
                    <Phone className="h-6 w-6 text-care-red mr-3 mt-1 flex-shrink-0" />
                    <div>
                      <p className="font-source-sans font-semibold text-trust-navy">Phone</p>
                      <a href="tel:(607)608-3292" className="font-open-sans text-medical-blue hover:underline">
                        (*************
                      </a>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <MapPin className="h-6 w-6 text-care-red mr-3 mt-1 flex-shrink-0" />
                    <div>
                      <p className="font-source-sans font-semibold text-trust-navy">Address</p>
                      <p className="font-open-sans text-comfort-gray">
                        865 US-11<br />
                        Kirkwood, NY 13795
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <Clock className="h-6 w-6 text-care-red mr-3 mt-1 flex-shrink-0" />
                    <div>
                      <p className="font-source-sans font-semibold text-trust-navy">Hours</p>
                      <p className="font-open-sans text-comfort-gray">
                        Monday - Friday: 8:00 AM - 5:00 PM<br />
                        Same-day appointments available
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <Calendar className="h-6 w-6 text-care-red mr-3 mt-1 flex-shrink-0" />
                    <div>
                      <p className="font-source-sans font-semibold text-trust-navy">Patient Portal</p>
                      <a 
                        href="https://31128.portal.athenahealth.com/" 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="font-open-sans text-medical-blue hover:underline"
                      >
                        Access Your Health Records
                      </a>
                    </div>
                  </div>
                </div>
                
                <div className="mt-8 pt-6 border-t border-gray-200">
                  <h4 className="font-source-sans font-semibold text-trust-navy mb-3">
                    Insurance Accepted
                  </h4>
                  <p className="font-open-sans text-sm text-comfort-gray">
                    Aetna, CI, Empire BCBS, Fidelis Care, Humana, Medicare, Optum, and most major insurance plans
                  </p>
                </div>
              </div>
              
              <div className="bg-medical-blue rounded-lg p-8 text-white">
                <h3 className="font-source-sans font-bold text-xl mb-4">
                  Why Families Choose Us
                </h3>
                <ul className="space-y-3">
                  {[
                    'Same-day appointments available for urgent needs',
                    'Comprehensive insurance acceptance',
                    'Multi-generational family care expertise',
                    'Local community commitment',
                    'Advanced patient portal technology'
                  ].map((benefit, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-healing-teal mr-3 mt-0.5 flex-shrink-0" />
                      <span className="font-open-sans">{benefit}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}