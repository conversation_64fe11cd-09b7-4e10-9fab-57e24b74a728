import Link from 'next/link';
import Image from 'next/image';
import { Phone, MapPin, Clock, Heart, Calendar } from 'lucide-react';

export default function Footer() {
  return (
    <footer className="bg-trust-navy text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Practice Info */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10">
                <Image
                  src="/images/kirkwood-medical-practice-logo.png"
                  alt="Kirkwood Medical Practice Logo"
                  width={40}
                  height={40}
                  className="w-10 h-10 object-contain"
                />
              </div>
              <div>
                <h3 className="font-source-sans font-bold text-lg text-white">
                  KIRKWOOD MEDICAL PRACTICE
                </h3>
              </div>
            </div>
            
            <p className="text-gray-300 mb-6 font-open-sans leading-relaxed max-w-md">
              Providing comprehensive primary care services to Broome County families. 
              Our experienced providers deliver personalized healthcare with the community 
              connection that only a local practice can provide.
            </p>

            <div className="space-y-3">
              <div className="flex items-center">
                <MapPin className="h-5 w-5 text-healing-teal mr-3" />
                <span className="font-open-sans">865 US-11, Kirkwood, NY 13795</span>
              </div>
              <a 
                href="tel:(607)608-3292" 
                className="flex items-center hover:text-healing-teal transition-colors"
              >
                <Phone className="h-5 w-5 text-healing-teal mr-3" />
                <span className="font-open-sans">(*************</span>
              </a>
              <div className="flex items-center">
                <Clock className="h-5 w-5 text-healing-teal mr-3" />
                <span className="font-open-sans">Monday - Friday: 8:00 AM - 5:00 PM</span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="font-source-sans font-semibold text-lg mb-4">Quick Links</h4>
            <nav className="space-y-3">
              {[
                { name: 'About Our Practice', href: '/about' },
                { name: 'Our Services', href: '/services' },
                { name: 'Service Areas', href: '/locations' },
                { name: 'Contact Us', href: '/contact' },
                { name: 'Patient Portal', href: 'https://31128.portal.athenahealth.com/', external: true },
              ].map((item) => (
                <div key={item.name}>
                  {item.external ? (
                    <a
                      href={item.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="block font-open-sans text-gray-300 hover:text-healing-teal transition-colors"
                    >
                      {item.name}
                    </a>
                  ) : (
                    <Link
                      href={item.href}
                      className="block font-open-sans text-gray-300 hover:text-healing-teal transition-colors"
                    >
                      {item.name}
                    </Link>
                  )}
                </div>
              ))}
            </nav>
          </div>

          {/* Services */}
          <div>
            <h4 className="font-source-sans font-semibold text-lg mb-4">Our Services</h4>
            <nav className="space-y-3">
              {[
                'Same-Day Appointments',
                'Annual Physicals',
                'Chronic Care Management',
                'Women\'s Health',
                'Telemedicine',
                'Family Medicine',
              ].map((service) => (
                <div key={service} className="font-open-sans text-gray-300">
                  {service}
                </div>
              ))}
            </nav>

            <div className="mt-6">
              <a
                href="https://consumer.scheduling.athena.io/?locationId=31128-1"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center bg-care-red hover:bg-red-700 text-white font-source-sans font-semibold px-4 py-2 rounded-lg transition-colors duration-200 min-h-[44px] focus:outline-none focus:ring-2 focus:ring-care-red focus:ring-offset-2 focus:ring-offset-trust-navy"
              >
                <Calendar className="h-4 w-4 mr-2" />
                Schedule Today
              </a>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-600 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="font-open-sans text-gray-300 text-sm">
              © 2025 Kirkwood Medical Practice. All rights reserved.
            </p>
            
            <div className="flex space-x-6 mt-4 md:mt-0">
              <span className="font-open-sans text-gray-300 text-sm">
                Serving Kirkwood, Binghamton & Conklin
              </span>
            </div>
          </div>
          
          <div className="mt-4 text-center md:text-left">
            <p className="font-open-sans text-gray-400 text-xs">
              Insurance Accepted: Aetna, CI, Empire BCBS, Fidelis Care, Humana, Medicare, Optum
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}