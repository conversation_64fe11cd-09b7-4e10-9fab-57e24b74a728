'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { Phone, Menu, X, Heart, Calendar, MapPin } from 'lucide-react';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const pathname = usePathname();

  const navigation = [
    { name: 'Home', href: '/' },
    { name: 'About Us', href: '/about' },
    { name: 'Services', href: '/services' },
    { name: 'Contact', href: '/contact' },
  ];

  const isActive = (path: string) => {
    if (path === '/') return pathname === '/';
    return pathname.startsWith(path);
  };

  return (
    <header className="bg-clean-white shadow-sm sticky top-0 z-50">
      {/* Top Bar */}
      <div className="bg-medical-blue text-white py-2">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center text-sm">
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                <MapPin className="h-4 w-4 mr-1" />
                <span>865 US-11, Kirkwood, NY 13795</span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <a 
                href="https://31128.portal.athenahealth.com/" 
                target="_blank" 
                rel="noopener noreferrer"
                className="hover:underline flex items-center"
              >
                <Calendar className="h-4 w-4 mr-1" />
                Patient Portal
              </a>
              <a 
                href="tel:(607)608-3292" 
                className="font-semibold hover:underline flex items-center"
              >
                <Phone className="h-4 w-4 mr-1" />
                (*************
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-6">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center">
              <div className="relative w-12 h-12">
                <Image
                  src="/images/kirkwood-medical-practice-logo.png"
                  alt="Kirkwood Medical Practice Logo"
                  width={48}
                  height={48}
                  className="w-12 h-12 object-contain"
                />
              </div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={`font-source-sans font-semibold text-base transition-colors duration-200 ${
                  isActive(item.href)
                    ? 'text-medical-blue border-b-2 border-medical-blue pb-1'
                    : 'text-trust-navy hover:text-medical-blue'
                }`}
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* CTA Button - Desktop */}
          <div className="hidden md:block">
            <a
              href="https://consumer.scheduling.athena.io/?locationId=31128-1"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-care-red hover:bg-red-700 text-white font-source-sans font-semibold px-6 py-3 rounded-lg transition-colors duration-200 min-w-[44px] min-h-[44px] flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-care-red focus:ring-offset-2"
            >
              Schedule Appointment
            </a>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden p-2 rounded-lg text-trust-navy hover:text-medical-blue hover:bg-gray-100 transition-colors duration-200 min-w-[44px] min-h-[44px] flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-medical-blue focus:ring-offset-2"
            aria-label="Toggle menu"
          >
            {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="md:hidden border-t border-gray-200">
          <div className="px-4 pt-2 pb-3 space-y-1 bg-white">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                onClick={() => setIsMenuOpen(false)}
                className={`block px-3 py-2 text-base font-source-sans font-semibold rounded-lg transition-colors duration-200 ${
                  isActive(item.href)
                    ? 'text-medical-blue bg-blue-50'
                    : 'text-trust-navy hover:text-medical-blue hover:bg-gray-50'
                }`}
              >
                {item.name}
              </Link>
            ))}
            <div className="pt-4">
              <a
                href="https://consumer.scheduling.athena.io/?locationId=31128-1"
                target="_blank"
                rel="noopener noreferrer"
                className="block w-full text-center bg-care-red hover:bg-red-700 text-white font-source-sans font-semibold px-6 py-3 rounded-lg transition-colors duration-200 min-h-[44px] focus:outline-none focus:ring-2 focus:ring-care-red focus:ring-offset-2"
                onClick={() => setIsMenuOpen(false)}
              >
                Schedule Appointment
              </a>
            </div>
          </div>
        </div>
      )}
    </header>
  );
}