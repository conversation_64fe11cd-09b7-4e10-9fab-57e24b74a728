import Link from 'next/link';
import { ArrowRight, Phone, Calendar, UserPlus } from 'lucide-react';

interface CTAProps {
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  href?: string;
  external?: boolean;
  onClick?: () => void;
  icon?: 'arrow' | 'phone' | 'calendar' | 'user' | 'none';
  className?: string;
  disabled?: boolean;
}

export default function CTA({
  variant = 'primary',
  size = 'md',
  children,
  href,
  external = false,
  onClick,
  icon = 'arrow',
  className = '',
  disabled = false,
}: CTAProps) {
  const baseClasses = 'inline-flex items-center justify-center font-source-sans font-semibold transition-all duration-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 min-w-[44px] min-h-[44px] disabled:opacity-50 disabled:cursor-not-allowed';

  const variants = {
    primary: 'bg-care-red hover:bg-red-700 text-white focus:ring-care-red',
    secondary: 'bg-medical-blue hover:bg-blue-800 text-white focus:ring-medical-blue',
    outline: 'border-2 border-medical-blue text-medical-blue hover:bg-medical-blue hover:text-white focus:ring-medical-blue',
  };

  const sizes = {
    sm: 'px-4 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-4 text-lg',
  };

  const iconComponents = {
    arrow: <ArrowRight className="h-4 w-4 ml-2" />,
    phone: <Phone className="h-4 w-4 mr-2" />,
    calendar: <Calendar className="h-4 w-4 mr-2" />,
    user: <UserPlus className="h-4 w-4 mr-2" />,
    none: null,
  };

  const classes = `${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`;

  const content = (
    <>
      {icon === 'phone' || icon === 'calendar' || icon === 'user' ? iconComponents[icon] : null}
      {children}
      {icon === 'arrow' ? iconComponents[icon] : null}
    </>
  );

  if (href) {
    if (external) {
      return (
        <a
          href={href}
          target="_blank"
          rel="noopener noreferrer"
          className={classes}
          onClick={onClick}
        >
          {content}
        </a>
      );
    } else {
      return (
        <Link href={href} className={classes} onClick={onClick}>
          {content}
        </Link>
      );
    }
  }

  return (
    <button
      type="button"
      className={classes}
      onClick={onClick}
      disabled={disabled}
    >
      {content}
    </button>
  );
}

// Pre-configured CTA components for common use cases
export const ScheduleAppointmentCTA = ({ className = '' }: { className?: string }) => (
  <CTA
    variant="primary"
    size="lg"
    href="https://consumer.scheduling.athena.io/?locationId=31128-1"
    external={true}
    icon="calendar"
    className={className}
  >
    Schedule Your Appointment
  </CTA>
);

export const CallNowCTA = ({ className = '' }: { className?: string }) => (
  <CTA
    variant="secondary"
    size="md"
    href="tel:(607)608-3292"
    external={true}
    icon="phone"
    className={className}
  >
    Call (*************
  </CTA>
);

export const NewPatientCTA = ({ className = '' }: { className?: string }) => (
  <CTA
    variant="outline"
    size="md"
    href="/contact"
    icon="user"
    className={className}
  >
    New Patients Welcome
  </CTA>
);

export const LearnMoreCTA = ({ href, children, className = '' }: { href: string; children: React.ReactNode; className?: string }) => (
  <CTA
    variant="outline"
    size="sm"
    href={href}
    icon="arrow"
    className={className}
  >
    {children}
  </CTA>
);