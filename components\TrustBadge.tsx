import { Shield, Award, Users, Clock, Heart, CheckCircle } from 'lucide-react';

interface TrustBadgeProps {
  type: 'insurance' | 'experience' | 'availability' | 'quality' | 'community' | 'certification';
  title: string;
  description: string;
  icon?: React.ReactNode;
  className?: string;
}

export default function TrustBadge({ 
  type, 
  title, 
  description, 
  icon, 
  className = '' 
}: TrustBadgeProps) {
  const iconMap = {
    insurance: <Shield className="h-6 w-6" />,
    experience: <Award className="h-6 w-6" />,
    availability: <Clock className="h-6 w-6" />,
    quality: <Heart className="h-6 w-6" />,
    community: <Users className="h-6 w-6" />,
    certification: <CheckCircle className="h-6 w-6" />,
  };

  const colorMap = {
    insurance: 'text-medical-blue bg-blue-50',
    experience: 'text-healing-teal bg-teal-50',
    availability: 'text-care-red bg-red-50',
    quality: 'text-medical-blue bg-blue-50',
    community: 'text-healing-teal bg-teal-50',
    certification: 'text-green-600 bg-green-50',
  };

  return (
    <div className={`flex items-start space-x-4 p-4 rounded-lg bg-white shadow-sm border border-gray-100 ${className}`}>
      <div className={`flex-shrink-0 w-12 h-12 rounded-lg flex items-center justify-center ${colorMap[type]}`}>
        {icon || iconMap[type]}
      </div>
      <div className="flex-1 min-w-0">
        <h4 className="font-source-sans font-semibold text-trust-navy text-sm mb-1">
          {title}
        </h4>
        <p className="font-open-sans text-comfort-gray text-sm leading-relaxed">
          {description}
        </p>
      </div>
    </div>
  );
}

// Pre-configured Trust Badge components
export const InsuranceTrustBadge = ({ className = '' }: { className?: string }) => (
  <TrustBadge
    type="insurance"
    title="Most Insurance Accepted"
    description="We accept Aetna, Empire BCBS, Medicare, Humana, and most major insurance plans"
    className={className}
  />
);

export const ExperienceTrustBadge = ({ className = '' }: { className?: string }) => (
  <TrustBadge
    type="experience"
    title="Experienced Providers"
    description="Decades of combined medical experience serving Broome County families"
    className={className}
  />
);

export const AvailabilityTrustBadge = ({ className = '' }: { className?: string }) => (
  <TrustBadge
    type="availability"
    title="Same-Day Appointments"
    description="When you're sick, we'll see you today. No long waits or delays"
    className={className}
  />
);

export const QualityTrustBadge = ({ className = '' }: { className?: string }) => (
  <TrustBadge
    type="quality"
    title="Personalized Care"
    description="Individual attention and care plans tailored to your unique health needs"
    className={className}
  />
);

export const CommunityTrustBadge = ({ className = '' }: { className?: string }) => (
  <TrustBadge
    type="community"
    title="Local Community Focus"
    description="Deep roots in Broome County with commitment to multi-generational family care"
    className={className}
  />
);

export const CertificationTrustBadge = ({ className = '' }: { className?: string }) => (
  <TrustBadge
    type="certification"
    title="Board Certified Providers"
    description="All providers are board certified with specialized training in family medicine"
    className={className}
  />
);